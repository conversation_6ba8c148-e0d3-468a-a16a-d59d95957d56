提示词：

```
调用mcp 获取reddit数据库中最近一个月的数据， 挖掘需求，分析哪些可以开发web网站解决需求盈利的。注意目的是盈利，需要挖掘数据中的需求。 将内容汇总到md文档中



使用MCP工具查询reddit数据库中最近6个月的数据，深度分析以下内容：

分析目标：
- 深度挖掘帖子内容中体现的用户需求和痛点
- 识别可以通过开发web网站/应用解决的商业机会
- 重点关注具有盈利潜力的需求（用户付费意愿强、市场规模大、技术可实现）
- 分析热门话题和高互动内容背后的商业价值

数据筛选条件：
- 时间范围：最近6个月
- 重点关注：高点赞数、高评论数的热门帖子
- 分析维度：用户痛点、解决方案需求、付费意愿、市场规模

输出要求：
1. 创建详细的需求分析报告（Markdown格式）
2. 包含具体的数据支撑（帖子标题、作者、点赞数、评论数、原帖链接）
3. 提供可执行的web项目建议，包括技术实现方案和盈利模式
4. 按盈利潜力对发现的机会进行优先级排序
5. 将报告保存到 /Users/<USER>/fuwenhao/github/spider/R-reddit/ 目录

注意事项：
- 确保查询的数据库表名正确
- 优先分析高互动量的帖子
- 关注技术门槛适中、开发周期可控的项目机会
- 分析报告要具有可操作性，不只是数据罗列
- 重点挖掘用户愿意付费解决的真实需求 @/Users/<USER>/fuwenhao/github/spider/R-reddit/ 
```


```
智能时间管理SaaS， 查询相关的Reddit帖子数据，是如何描述需要这个需求的，最好有深度的分析，带有原帖子的数据信息url等。 将内容总结到新的md文档中


内容创作者工具套件,  查询相关的Reddit帖子数据，是如何描述需要这个需求的，最好有深度的分析，带有原帖子的数据信息url等。 将内容总结到新的md文档中 @/Users/<USER>/fuwenhao/github/spider/R-reddit/ 


小企业员工管理平台. 查询相关的Reddit帖子数据，是如何描述需要这个需求的，最好有深度的分析，带有原帖子的数据信息url等。 将内容总结到新的md文档中 @/Users/<USER>/fuwenhao/github/spider/R-reddit/ 
```